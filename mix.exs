defmodule Adyen.MixProject do
  use Mix.Project

  def project do
    [
      app: :adyen,
      version: "0.1.20",
      elixir: "~> 1.14",
      start_permanent: Mix.env() == :prod,
      deps: deps(),
      description: description(),
      package: package(),
      # Docs
      name: "Adyen Elixir API Library",
      source_url: "https://github.com/stagedates/ex_adyen_elixir_api_library",
      docs: [
        main: "Adyen Elixir API Library",
        #        logo: "",
        extras: ["README.md"]
      ]
    ]
  end

  # Run "mix help compile.app" to learn about applications.
  def application do
    [
      extra_applications: [:logger],
      mod: {Adyen.Application, []}
    ]
  end

  # Run "mix help deps" to learn about dependencies.
  defp deps do
    [
      {:tesla, "~> 1.4"},
      # optional, but recommended adapter
      {:hackney, "~> 1.17"},
      {:ex_doc, ">= 0.0.0", runtime: false},
      {:credo, "~> 1.7", only: [:dev, :test], runtime: false},
      {:versioce, "~> 0.2.1"}
    ]
  end

  defp description() do
    "Stagedates adyen API library."
  end

  defp package() do
    [
      # This option is only needed when you don't want to use the OTP application name
      name: "adyen",
      organization: "stagedates",
      # These are the default files included in the package
      files: ~w(lib .formatter.exs mix.exs README*),
      licenses: ["Proprietary"],
      links: %{"GitHub" => "https://github.com/stagedates/ex_adyen_elixir_api_library"}
    ]
  end
end
