# Adyen APIs Library for Elixir

This is the unofficially supported Elixir library for using Adyen's APIs.

## Supported API versions
| API | Description | Service Name | Supported version | Status |
| --- | ----------- | ------------ | ----------------- |--------|
|[BIN lookup API](https://docs.adyen.com/api-explorer/BinLookup/52/overview) | The BIN Lookup API provides endpoints for retrieving information based on a given BIN. | binLookup | **v52** | ?      |
| [Balance Platform API](https://docs.adyen.com/api-explorer/balanceplatform/2/overview) | The Balance Platform API enables you to create a platform where you can onboard your users as account holders and create balance accounts, cards, and business accounts. | balancePlatform | **v2** | ?      |
| [Checkout API](https://docs.adyen.com/api-explorer/Checkout/70/overview)| Our latest integration for accepting online payments. | checkout | **v70** | ?      |
| [Data Protection API](https://docs.adyen.com/development-resources/data-protection-api) | Endpoint for requesting data erasure. | dataProtection | **v1** | ?      |
| [Legal Entity Management API](https://docs.adyen.com/api-explorer/legalentity/3/overview) | Endpoint to manage legal entities | legalEntityManagement | **v3** | ?      |
| [Management API](https://docs.adyen.com/api-explorer/Management/1/overview)| Configure and manage your Adyen company and merchant accounts, stores, and payment terminals. | management | **v1** | ?      |
| [Payments API](https://docs.adyen.com/api-explorer/Payment/68/overview)| Our classic integration for online payments. | payments | **v68** | ?      |
| [Payouts API](https://docs.adyen.com/api-explorer/Payout/68/overview)| Endpoints for sending funds to your customers. | payouts | **v68** | ?      |
| [POS Terminal Management API](https://docs.adyen.com/api-explorer/postfmapi/1/overview)| Endpoints for managing your point-of-sale payment terminals. | terminal | **v1** | ?      |
| [Recurring API](https://docs.adyen.com/api-explorer/Recurring/68/overview)| Endpoints for managing saved payment details. | recurring | **v68** | ?      |
| [Stored Value API](https://docs.adyen.com/payment-methods/gift-cards/stored-value-api) | Endpoints for managing gift cards. | storedValue | **v46** | ?      |
| [Transfers API](https://docs.adyen.com/api-explorer/transfers/3/overview) | Endpoints for managing transfers, getting information about transactions or moving fund | transfers | **v3** | ?      |

For more information, refer to our [documentation](https://docs.adyen.com/) or the [API Explorer](https://docs.adyen.com/api-explorer/).


## Prerequisites

-   [Adyen test account](https://docs.adyen.com/get-started-with-adyen)
-   [API key](https://docs.adyen.com/development-resources/api-credentials#generate-api-key). For testing, your API credential needs to have the [API PCI Payments role](https://docs.adyen.com/development-resources/api-credentials#roles).
- Elixir 1.10 or higher
- Packages (?)

## Usage

### Setup
1. Add `adyen` to your list of dependencies in `mix.exs`:

```elixir
def deps do
  [
    {:adyen, "~> 0.1.20"}
  ]
end
```
2. Configure your adyen environments in `config/dev.exs` and later in `config/prod.exs`:
```elixir
config :adyen,
  environment: :test,
  merchant_account: System.fetch_env!("ADYEN_PAYMENTS_MERCHANT_ACCOUNT") ,
  api_key: [
    checkout_api_key: System.fetch_env!("ADYEN_PAYMENTS_API_KEY"),
    xxx_api_key: System.fetch_env!("ADYEN_XXX_API_KEY")
  ],
  live_prefix: System.fetch_env!("ADYEN_LIVE_URL_PREFIX")
```

### Consuming Services
Every API the library supports is represented by a service module. The name of the service matching the corresponding API is listed in the [Integrations](#supported-api-versions) section of this document.

#### Using one of the services
```elixir
def DummyModule  do
  @@moduledoc """
    Dummy Module
  """
   alias ExAdyenElixirApiLibrary.Services.{Checkout, Transfer}
   
   def create_transfer(params) do
     Transfer.transfer_funds(:payout_to_transfer_instrument, params)
   end
   
   def get_payment_methods(params) do
     Checkout.payment_methods(params)
   end
end

```
## Feedback
We value your input! Help us enhance our API Libraries and improve the integration experience by providing your feedback. Please take a moment to fill out [our feedback form](https://forms.gle/A4EERrR6CWgKWe5r9) to share your thoughts, suggestions or ideas.

## Contributing

We encourage you to contribute to this repository, so everyone can benefit from new features, bug fixes, and any other improvements.


Have a look at our [contributing guidelines](xxxxxxx) to find out how to raise a pull request.


## Support
If you have a feature request, or spotted a bug or a technical problem, [create an issue here](xxxxx).

For other questions, [contact our Support Team](xxxxx).


## Licence
This repository is available under the [MIT license](xxxxx).

## See also
* [Adyen docs](https://docs.adyen.com/)
* [API Explorer](https://docs.adyen.com/api-explorer/)
