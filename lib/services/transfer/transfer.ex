defmodule Adyen.Services.Transfer do
  @moduledoc false
  alias Adyen.Services.Transfer.{CapitalApi, TransactionsApi, TransfersApi}

  @callback get_capital_account() :: {:error, binary | %{body: any, status: any}} | {:ok, any}
  @callback get_grant_reference_details() ::
              {:error, binary | %{body: any, status: any}} | {:ok, any}
  @callback request_grant_payout() :: {:error, binary | %{body: any, status: any}} | {:ok, any}

  @callback get_all_transactions() :: {:error, binary | %{body: any, status: any}} | {:ok, any}

  @callback get_transaction() :: {:error, binary | %{body: any, status: any}} | {:ok, any}
  @callback transfer_funds(atom(), map()) ::
              {:error, binary | %{body: any, status: any}} | {:ok, any}

  @spec get_capital_account() :: {:error, binary | %{body: any, status: any}} | {:ok, any}
  defdelegate get_capital_account(), to: CapitalApi
  @spec get_grant_reference_details() :: {:error, binary | %{body: any, status: any}} | {:ok, any}
  defdelegate get_grant_reference_details(), to: CapitalApi
  @spec request_grant_payout() :: {:error, binary | %{body: any, status: any}} | {:ok, any}
  defdelegate request_grant_payout(), to: CapitalApi

  @spec get_all_transactions() :: {:error, binary | %{body: any, status: any}} | {:ok, any}
  defdelegate get_all_transactions(), to: TransactionsApi
  @spec get_transaction() :: {:error, binary | %{body: any, status: any}} | {:ok, any}
  defdelegate get_transaction(), to: TransactionsApi

  @spec transfer_funds(atom(), map()) :: {:error, binary | %{body: any, status: any}} | {:ok, any}
  defdelegate transfer_funds(type, params), to: TransfersApi
end
