defmodule Adyen.Services.Transfer.TransfersApi do
  @moduledoc false
  use Adyen.Clients.BaseClient
  require Logger

  def transfer_funds(type, params) do
    body = build_transfer_payload(type, params)
    Logger.debug("Performing transfer with following params: #{inspect(body)}")

    client(endpoint: :balance, api_key: :balance_api_key)
    |> Tesla.post("/transfers", body)
    |> parse_response()
  end

  defp build_transfer_payload(:cross_border_transfer, params) do
    %{
      amount: %{
        value: params.amount,
        currency: params.currency
      },
      balanceAccountId: params.balance_account_id,
      category: "bank",
      counterparty: %{
        bankAccount: %{
          accountHolder: %{
            fullName: params.full_name,
            address: %{
              city: params.address.city,
              postalCode: params.address.postal_code,
              stateOrProvince: params.address.state,
              country: params.address.country,
              line1: params.address.house_number,
              line2: params.address.street
            }
          },
          accountIdentification: %{
            type: "numberAndBic",
            accountNumber: params.account_number,
            bic: params.bic
          }
        }
      },
      priority: "crossBorder",
      referenceForBeneficiary: params.reference_for_beneficiary,
      reference: params.reference,
      description: params.description
    }
  end

  defp build_transfer_payload(:sepa_transfer, params) do
    %{
      amount: %{
        value: params.amount,
        currency: params.currency
      },
      balanceAccountId: params.balance_account_id,
      category: "bank",
      counterparty: %{
        bankAccount: %{
          accountHolder: %{
            fullName: params.full_name
          },
          accountIdentification: %{
            type: "iban",
            iban: params.iban
          }
        }
      },
      priority: "regular",
      referenceForBeneficiary: params.reference_for_beneficiary,
      reference: params.reference,
      description: params.description
    }
  end

  defp build_transfer_payload(:us_local_transfer, params) do
    %{
      amount: %{
        value: params.amount,
        currency: params.currency
      },
      balanceAccountId: params.balance_account_id,
      category: "bank",
      counterparty: %{
        bankAccount: %{
          accountHolder: %{
            fullName: params.full_name
          },
          accountIdentification: %{
            type: "usLocal",
            accountNumber: params.account_number,
            routingNumber: params.routing_number
          }
        }
      },
      priority: "regular",
      referenceForBeneficiary: params.reference_for_beneficiary,
      reference: params.reference,
      description: params.description
    }
  end

  defp build_transfer_payload(:b2b_transfer, params) do
    %{
      amount: %{
        value: params.amount,
        currency: params.currency
      },
      balanceAccountId: params.from_balance_account_id,
      category: "internal",
      counterparty: %{
        balanceAccountId: params.to_balance_account_id
      },
      referenceForBeneficiary: params.reference_for_beneficiary,
      reference: params.reference,
      description: params.description
    }
  end

  defp build_transfer_payload(:payout_to_transfer_instrument, params) do
    %{
      amount: %{
        value: params.amount,
        currency: params.currency
      },
      balanceAccountId: params.balance_account_id,
      category: "bank",
      counterparty: %{
        transferInstrumentId: params.transfer_instrument_id
      },
      priority: "regular",
      referenceForBeneficiary: params.reference_for_beneficiary,
      reference: params.reference,
      description: params.description
    }
  end
end
