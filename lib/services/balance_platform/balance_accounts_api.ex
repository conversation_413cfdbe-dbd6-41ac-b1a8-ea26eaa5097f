defmodule Adyen.Services.BalancePlatform.BalanceAccountsApi do
  @moduledoc false
  use Adyen.Clients.BaseClient
  require Logger

  def get_balance_account(id) do
    Logger.debug("Getting Balance Account with following id: #{inspect(id)}")

    client(endpoint: :balance_platform, api_key: :balance_api_key)
    |> Tesla.get("/balanceAccounts/#{id}")
    |> parse_response()
  end

  def create_balance_account(param) do
    Logger.debug(
      "Creating Balance Account with account holder  id: #{inspect(param.account_holder_id)}"
    )

    payload =
      %{
        accountHolderId: param.account_holder_id,
        reference: param.reference,
        description: param.description
      }

    client(endpoint: :balance_platform, api_key: :balance_api_key)
    |> Tesla.post("/balanceAccounts", payload)
    |> parse_response()
  end

  def update_balance_account(balance_account_id, param) do
    Logger.debug("Updating Balance Account with following id: #{inspect(balance_account_id)}")

    client(endpoint: :balance_platform, api_key: :balance_api_key)
    |> Tesla.patch("/balanceAccounts/#{balance_account_id}", %{description: param.description})
    |> parse_response()
  end
end
