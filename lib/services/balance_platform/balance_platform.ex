defmodule Adyen.Services.BalancePlatform do
  @moduledoc false
  alias Adyen.Services.BalancePlatform.{AccountHoldersApi, BalanceAccountsApi}

  @spec get_balance_account(any()) ::
          {:error, binary | %{body: any, status: any}} | {:ok, any}
  defdelegate get_balance_account(id), to: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

  @spec get_account_holder(any()) ::
          {:error, binary | %{body: any, status: any}} | {:ok, any}
  defdelegate get_account_holder(id), to: AccountHoldersApi

  @spec create_account_holder(map()) ::
          {:error, binary | %{body: any, status: any}} | {:ok, any}
  defdelegate create_account_holder(param), to: Account<PERSON><PERSON><PERSON><PERSON><PERSON>

  @spec create_balance_account(map()) ::
          {:error, binary | %{body: any, status: any}} | {:ok, any}
  defdelegate create_balance_account(param), to: BalanceAccountsApi

  @spec update_balance_account(balance_account_id :: String.t(), param :: map()) ::
          {:error, binary | %{body: any, status: any}} | {:ok, any}
  defdelegate update_balance_account(balance_account_id, param), to: BalanceAccountsApi
end
