defmodule Adyen.Services.BalancePlatform.AccountHoldersApi do
  @moduledoc false
  use Adyen.Clients.BaseClient
  require Logger

  def get_account_holder(id) do
    Logger.debug("Getting Legal Entity with following id: #{inspect(id)}")

    client(endpoint: :balance_platform, api_key: :balance_api_key)
    |> Tesla.get("/accountHolders/#{id}")
    |> parse_response()
  end

  def create_account_holder(param) do
    payload =
      %{
        legalEntityId: param.legal_entity_id,
        balancePlatform: param.balance_platform,
        reference: param.reference
      }

    client(endpoint: :balance_platform, api_key: :balance_api_key)
    |> Tesla.post("/accountHolders", payload)
    |> parse_response()
  end

  def get_all_balance_accounts_of_account_holder(_id) do
  end

  def get_tax_form(_id) do
  end

  def update_account_holder(_id, _params) do
  end
end
