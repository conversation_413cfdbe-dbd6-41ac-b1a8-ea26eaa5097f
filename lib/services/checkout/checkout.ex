defmodule Adyen.Services.Checkout do
  @moduledoc false
  alias Adyen.Services.Checkout.Payments

  @spec captures(map(), binary()) :: {:error, binary | %{body: any, status: any}} | {:ok, any}
  defdelegate captures(params, psp_reference), to: Payments

  @spec payment_methods(map()) :: {:error, binary | %{body: any, status: any}} | {:ok, any}
  defdelegate payment_methods(params), to: Payments

  @spec payments(map()) :: {:error, binary | %{body: any, status: any}} | {:ok, any}
  defdelegate payments(params), to: Payments

  @spec payments_details(map()) :: {:error, binary | %{body: any, status: any}} | {:ok, any}
  defdelegate payments_details(params), to: Payments

  @spec refunds(map(), any()) :: {:error, binary | %{body: any, status: any}} | {:ok, any}
  defdelegate refunds(params, psp_reference), to: Payments

  @spec reversals(map(), any()) :: {:error, binary | %{body: any, status: any}} | {:ok, any}
  defdelegate reversals(params, psp_reference), to: Payments
end
