defmodule Adyen.Services.Checkout.Payments do
  @moduledoc false
  use Adyen.Clients.BaseClient
  require Logger

  def payment_methods(params) do
    merchant_account = config() |> Keyword.get(:merchant_account)

    body = %{
      merchantAccount: merchant_account,
      amount: %{
        value: params.amount.value,
        currency: params.amount.currency
      },
      channel: params.channel,
      countryCode: params.country_code,
      shopperLocale: params.shopper_locale
    }

    client(endpoint: :checkout, api_key: :checkout_api_key)
    |> Tesla.post("/paymentMethods", body)
    |> parse_response()
  end

  def payments(params) do
    merchant_account = config() |> Keyword.get(:merchant_account)

    body = %{
      merchantAccount: merchant_account,
      paymentMethod: params.payment_data,
      returnUrl: params.return_url,
      amount: %{
        value: params.amount.value,
        currency: params.amount.currency
      },
      splits: params.splits,
      channel: params.channel,
      shopperLocale: params.shopper.locale,
      shopperEmail: params.shopper.email,
      shopperIP: params.shopper.ip,
      deliveryDate: params.delivery_date,
      origin: params.origin,
      browserInfo: params.browser_info,
      platformChargebackLogic: %{
        behavior: params.platform_chargeback_logic.behavior
      },
      shopperName: %{
        firstName: params.shopper.first_name,
        lastName: params.shopper.last_name
      },
      lineItems: params.line_items,
      countryCode: params.country_code,
      billingAddress: %{
        country: params.billing_address.country,
        city: params.billing_address.city,
        postalCode: params.billing_address.postal_code,
        street: params.billing_address.street,
        houseNumberOrName: params.billing_address.house_number_or_name
      },
      reference: params.reference
    }

    request_body =
      body
      |> maybe_set_company(params)
      |> maybe_set_additional_data(params)
      |> maybe_set_manual_capture(params)

    client(endpoint: :checkout, api_key: :checkout_api_key)
    |> Tesla.post("/payments", request_body)
    |> parse_response()
  end

  def payments_details(params) do
    client(endpoint: :checkout, api_key: :checkout_api_key)
    |> Tesla.post("/payments/details", params)
    |> parse_response()
  end

  def refunds(params, psp_reference) do
    merchant_account = config() |> Keyword.get(:merchant_account)

    body =
      maybe_set_splits(
        %{
          merchantAccount: merchant_account,
          amount: %{
            value: params.amount.value,
            currency: params.amount.currency
          },
          merchantRefundReason: params.merchant_refund_reason,
          reference: params.reference
        },
        params
      )

    client(endpoint: :checkout, api_key: :checkout_api_key)
    |> Tesla.post("/payments/#{psp_reference}/refunds", body)
    |> parse_response()
  end

  def reversals(reference, psp_reference) do
    merchant_account = config() |> Keyword.get(:merchant_account)

    body = %{
      merchantAccount: merchant_account,
      reference: reference
    }

    client(endpoint: :checkout, api_key: :checkout_api_key)
    |> Tesla.post("/payments/#{psp_reference}/reversals", body)
    |> parse_response()
  end

  def captures(params, psp_reference) do
    merchant_account = config() |> Keyword.get(:merchant_account)

    body =
      %{
        merchantAccount: merchant_account,
        amount: %{
          value: params.amount.value,
          currency: params.amount.currency
        },
        reference: params.reference
      }

    client(endpoint: :checkout, api_key: :checkout_api_key)
    |> Tesla.post("/payments/#{psp_reference}/captures", body)
    |> parse_response()
  end

  def sessions() do
  end

  def get_result_of_payment_session() do
  end

  def card_details() do
  end

  def donations() do
  end

  defp maybe_set_splits(body, %{splits: splits})
       when not is_nil(splits),
       do: Map.put(body, :splits, splits)

  defp maybe_set_splits(body, _params), do: body

  defp maybe_set_company(body, %{billing_address: %{company: company}} = _params),
    do: Map.put(body, :company, company)

  defp maybe_set_company(body, %{billing_address: _billing_address} = _params), do: body

  defp maybe_set_additional_data(body, %{payment_method: "paypal"} = params),
    do: Map.put(body, :additionalData, params.additional_data)

  defp maybe_set_additional_data(body, %{payment_method: _payment_method} = _params), do: body

  defp maybe_set_manual_capture(body, %{manual_capture: true} = _params) do
    Map.put(body, :additionalData, %{manualCapture: true})
  end

  defp maybe_set_manual_capture(body, _params), do: body
end
