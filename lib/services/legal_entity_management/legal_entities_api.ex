defmodule Adyen.Services.LegalEntityManagement.LegalEntitiesApi do
  @moduledoc false
  use Adyen.Clients.BaseClient
  require Logger

  def get_legal_entity(id) do
    Logger.debug("Getting Legal Entity with following id: #{inspect(id)}")

    client(endpoint: :lem, api_key: :kyc_api_key)
    |> Tesla.get("/legalEntities/#{id}")
    |> parse_response()
  end

  def create_legal_entity(param, type) do
    Logger.debug("Create legal entity of type #{type} for following param: #{inspect(param)}")
    payload = create_payload_base_on_type(type, param)

    client(endpoint: :lem, api_key: :kyc_api_key)
    |> Tesla.post("/legalEntities", payload)
    |> parse_response()
  end

  def create_sole_proprietor_legal_entity(param) do
    payload = %{
      "type" => "individual",
      "individual" => %{
        "name" => %{
          "firstName" => param.first_name,
          "lastName" => param.last_name
        },
        "residentialAddress" => %{"country" => param.country}
      },
      "entityAssociations" => [
        %{"legalEntityId" => param.legal_entity, "type" => "soleProprietorship"}
      ]
    }

    client(endpoint: :lem, api_key: :kyc_api_key)
    |> Tesla.post("/legalEntities", payload)
    |> parse_response()
  end

  def update_legal_entity(param, type) do
    update_payload_base_on_type(param, type)
  end

  def get_all_business_lines_under_legal_entity(id) do
    Logger.debug("Get all business lines under a legal entity with following id: #{inspect(id)}")

    client(endpoint: :lem, api_key: :kyc_api_key)
    |> Tesla.post("/legalEntities/#{id}/businessLines")
    |> parse_response()
  end

  def check_legal_entity_verification_errors(id) do
    Logger.debug("Check a legal entity's verification errors with following id: #{inspect(id)}")

    client(endpoint: :lem, api_key: :kyc_api_key)
    |> Tesla.post("/legalEntities/#{id}/checkVerificationErrors")
    |> parse_response()
  end

  defp create_payload_base_on_type(type, param) do
    case type do
      :organization ->
        %{
          "type" => "organization",
          "organization" => %{
            "legalName" => param.legal_name,
            "registeredAddress" => %{"country" => param.country}
          }
        }

      :individual ->
        %{
          "type" => "individual",
          "individual" => %{
            "name" => %{
              "firstName" => param.first_name,
              "lastName" => param.last_name
            },
            "residentialAddress" => %{"country" => param.country}
          }
        }

      :sole_proprietorship ->
        %{
          "type" => "soleProprietorship",
          "soleProprietorship" => %{
            "name" => param.company_name,
            "countryOfGoverningLaw" => param.country,
            "registeredAddress" => %{"country" => param.country}
          }
        }
    end
  end

  defp update_payload_base_on_type(type, param) do
    case type do
      :change_organization_to_indivitual ->
        %{"type" => "individual"}

      :add_entity_association_to_organization ->
        %{
          "entityAssociations" => %{
            "jobTitle" => param.job_title,
            "legalEntityId" => param.legal_entity_id,
            "type" => "uboThroughControl"
          }
        }

      :add_entity_association_to_proprietorship ->
        %{
          "entityAssociations" => %{
            "legalEntityId" => param.legal_entity_id,
            "type" => "soleProprietorship"
          }
        }

      :associate_a_trust_member_with_a_trust ->
        %{
          "entityAssociations" => %{
            "legalEntityId" => param.legal_entity_id,
            "type" => "protector"
          }
        }

      :associate_an_exempt_settlor_with_a_trust ->
        %{
          "entityAssociations" => %{
            "legalEntityId" => param.legal_entity_id,
            "type" => "settlor",
            "entityType" => "individual",
            "settlorExemptionReason" => ["deceased", "professionalServiceProvider"]
          }
        }
    end
  end
end
