defmodule Adyen.Services.LegalEntityManagement.HostedOnboardingApi do
  @moduledoc false
  use Adyen.Clients.BaseClient
  require Logger

  def create_hosted_onboarding_link(id, params) do
    Logger.debug(
      "Get a link to an Adyen-hosted onboarding page for following legal entity: #{inspect(id)}"
    )

    body =
      %{
        "themeId" => params.theme_id,
        "redirectUrl" => params.return_url
      }
      |> maybe_add_settings(params)

    client(endpoint: :lem, api_key: :kyc_api_key)
    |> Tesla.post("/legalEntities/#{id}/onboardingLinks", body)
    |> parse_response()
  end

  defp maybe_add_settings(body, %{settings: settings}) when not is_nil(settings) do
    Map.put(body, "settings", settings)
  end

  defp maybe_add_settings(body, _), do: body
end
