defmodule Adyen.Services.LegalEntityManagement do
  @moduledoc false
  alias Adyen.Services.LegalEntityManagement.{
    HostedOnboardingApi,
    LegalEntitiesApi,
    TransferInstrumentApi
  }

  @callback get_legal_entity(any) :: {:error, binary | %{body: any, status: any}} | {:ok, any}
  @callback get_transfer_instrument(any) ::
              {:error, binary | %{body: any, status: any}} | {:ok, any}

  @callback create_legal_entity(map(), atom()) ::
              {:error, binary | %{body: any, status: any}} | {:ok, any}
  @callback update_legal_entity(map(), atom()) ::
              {:error, binary | %{body: any, status: any}} | {:ok, any}
  @callback get_all_business_lines_under_legal_entity(any()) ::
              {:error, binary | %{body: any, status: any}} | {:ok, any}
  @callback check_legal_entity_verification_errors(any()) ::
              {:error, binary | %{body: any, status: any}} | {:ok, any}

  @callback create_hosted_onboarding_link(any(), map()) ::
              {:error, binary | %{body: any, status: any}} | {:ok, any}

  @callback create_sole_proprietor_legal_entity(any()) ::
              {:error, binary | %{body: any, status: any}} | {:ok, any}

  @spec get_legal_entity(any) ::
          {:error, binary | %{body: any, status: any}} | {:ok, any}
  defdelegate get_legal_entity(id), to: LegalEntitiesApi

  @spec get_transfer_instrument(any) ::
          {:error, binary | %{body: any, status: any}} | {:ok, any}
  defdelegate get_transfer_instrument(id), to: TransferInstrumentApi

  @spec create_legal_entity(map(), atom()) ::
          {:error, binary | %{body: any, status: any}} | {:ok, any}
  defdelegate create_legal_entity(params, type), to: LegalEntitiesApi

  @spec create_sole_proprietor_legal_entity(map()) ::
          {:error, binary | %{body: any, status: any}} | {:ok, any}
  defdelegate create_sole_proprietor_legal_entity(params), to: LegalEntitiesApi

  @spec update_legal_entity(map(), atom()) ::
          {:error, binary | %{body: any, status: any}} | {:ok, any}
  defdelegate update_legal_entity(params, type), to: LegalEntitiesApi

  @spec get_all_business_lines_under_legal_entity(any()) ::
          {:error, binary | %{body: any, status: any}} | {:ok, any}
  defdelegate get_all_business_lines_under_legal_entity(id), to: LegalEntitiesApi

  @spec check_legal_entity_verification_errors(any()) ::
          {:error, binary | %{body: any, status: any}} | {:ok, any}
  defdelegate check_legal_entity_verification_errors(id), to: LegalEntitiesApi

  @spec create_hosted_onboarding_link(any(), map()) ::
          {:error, binary | %{body: any, status: any}} | {:ok, any}
  defdelegate create_hosted_onboarding_link(id, params), to: HostedOnboardingApi
end
