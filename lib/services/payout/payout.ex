defmodule Adyen.Services.Payout do
  @moduledoc false
  alias Adyen.Services.Payout.{
    InitializationAPI,
    InstantPayoutsApi,
    ReviewingApi
  }

  @callback store_detail() :: {:error, binary | %{body: any, status: any}} | {:ok, any}
  @callback store_detail_and_submit_third_party() ::
              {:error, binary | %{body: any, status: any}} | {:ok, any}
  @callback submit_third_party() :: {:error, binary | %{body: any, status: any}} | {:ok, any}

  @callback payout(map(), keyword()) :: {:error, binary | %{body: any, status: any}} | {:ok, any}

  @callback confirm_third_party() :: {:error, binary | %{body: any, status: any}} | {:ok, any}
  @callback decline_third_party() :: {:error, binary | %{body: any, status: any}} | {:ok, any}

  @spec store_detail() :: {:error, binary | %{body: any, status: any}} | {:ok, any}
  defdelegate store_detail(), to: InitializationAPI

  @spec store_detail_and_submit_third_party() ::
          {:error, binary | %{body: any, status: any}} | {:ok, any}
  defdelegate store_detail_and_submit_third_party(), to: InitializationAPI
  @spec submit_third_party() :: {:error, binary | %{body: any, status: any}} | {:ok, any}
  defdelegate submit_third_party(), to: InitializationAPI

  @spec payout(map(), keyword()) :: {:error, binary | %{body: any, status: any}} | {:ok, any}
  defdelegate payout(params, opts), to: InstantPayoutsApi

  @spec confirm_third_party() :: {:error, binary | %{body: any, status: any}} | {:ok, any}
  defdelegate confirm_third_party(), to: ReviewingApi

  @spec decline_third_party() :: {:error, binary | %{body: any, status: any}} | {:ok, any}
  defdelegate decline_third_party(), to: ReviewingApi
end
