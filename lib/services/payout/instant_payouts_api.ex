defmodule Adyen.Services.Payout.InstantPayoutsApi do
  use Adyen.Clients.BaseClient
  @moduledoc false
  require Logger

  def payout(params, _opts) do
    body = %{
      amount: %{
        value: params.amount,
        currency: params.currency
      },
      card: %{
        number: params.card.number,
        expiryMonth: params.card.expiry_month,
        expiryYear: params.card.expiry_year,
        holderName: params.card.holder_name
      },
      billingAddress: %{
        houseNumberOrName: params.billing_address.house_number,
        street: params.billing_address.street,
        city: params.billing_address.city,
        postalCode: params.billing_address.postal_code,
        stateOrProvince: params.billing_address.state,
        country: params.billing_address.country
      },
      merchantAccount: params.merchant_account,
      reference: params.reference,
      shopperName: %{
        firstName: params.shopper_name.firstname,
        lastName: params.shopper_name.lastname
      },
      dateOfBirth: params.date_of_birth,
      nationality: params.nationality
    }

    Logger.debug("Performing Transfer with following params: #{inspect(body)}")

    client(endpoint: :payout, api_key: :balance_api_key)
    |> Tesla.post("/payout", body)
    |> parse_response()
  end
end
