defmodule EventsService.Vendor.Promoter do
  @moduledoc false
  use Ecto.Schema

  import Ecto.Changeset

  alias EventsService.Repo
  alias EventsService.Seller.Organizer
  alias EventsService.Vendor.Promoter

  @schema_prefix :events
  @primary_key {:id, :binary_id, autogenerate: true}
  @foreign_key_type :binary_id

  # styler:sort
  schema "promoters" do
    belongs_to :address, EventsService.Addresses.Address

    field :account_holder_id, :string
    field :balance_account_id, :string
    field :company_name, :string
    field :created_by, Ecto.UUID
    field :created_by_document_id, :string
    field :deleted_at, :utc_datetime
    field :display_name, :string
    field :entity_type, Ecto.Enum, values: [:organization, :individual, :soleProprietorship]
    field :family_name, :string
    field :firestore_id, :string
    field :given_name, :string
    field :is_small_business, :boolean, default: false
    field :is_verified, :boolean, default: false
    field :legal_entity_id, :string
    field :owner_id, Ecto.UUID
    field :short_code, :string
    field :store_url, :string
    field :tax_id, :string
    field :vat_id, :string

    has_many :promoter_users, EventsService.Vendor.PromoterUser
    has_one :organizer, Organizer

    has_one :seatsio_workspace_keys, EventsService.Vendor.SeatsioWorkspaceKeys

    timestamps()
  end

  @type t :: %Promoter{
          account_holder_id: String.t(),
          address_id: Ecto.UUID.t(),
          balance_account_id: String.t(),
          company_name: String.t(),
          created_by_document_id: String.t(),
          created_by: Ecto.UUID.t(),
          deleted_at: DateTime.t(),
          display_name: String.t(),
          entity_type: atom(),
          family_name: String.t(),
          firestore_id: String.t(),
          given_name: String.t(),
          is_small_business: boolean(),
          is_verified: boolean(),
          legal_entity_id: String.t(),
          owner_id: Ecto.UUID.t(),
          short_code: String.t(),
          store_url: String.t(),
          tax_id: String.t(),
          vat_id: String.t()
        }

  @doc false
  def changeset(promoter, attrs) do
    promoter
    |> cast(attrs, [
      :firestore_id,
      :display_name,
      :given_name,
      :family_name,
      :company_name,
      :store_url,
      :account_holder_id,
      :balance_account_id,
      :legal_entity_id,
      :tax_id,
      :entity_type,
      :address_id,
      :owner_id,
      :created_by,
      :is_verified,
      :deleted_at,
      :short_code,
      :is_small_business
    ])
    |> validate_required([
      :account_holder_id,
      :balance_account_id,
      :display_name,
      :legal_entity_id
    ])
    |> unique_constraint(:short_code)
  end

  def create_changeset(promoter, %{"entity_type" => "individual"} = attrs) do
    promoter
    |> cast(attrs, [
      :display_name,
      :given_name,
      :family_name,
      :company_name,
      :tax_id,
      :vat_id,
      :entity_type,
      :created_by,
      :created_by_document_id,
      :is_small_business,
      :short_code
    ])
    |> validate_required([
      :display_name,
      :given_name,
      :family_name,
      :tax_id,
      :entity_type
    ])
    |> validate_at_least_one_created_by_identifier()
    |> unique_constraint(:created_by_document_id)
    |> unique_constraint(:created_by, name: :unique_promoter_created_by)
    |> unique_constraint(:short_code)
  end

  def create_changeset(promoter, %{"entity_type" => "soleProprietorship"} = attrs) do
    promoter
    |> cast(attrs, [
      :display_name,
      :given_name,
      :family_name,
      :company_name,
      :tax_id,
      :vat_id,
      :entity_type,
      :created_by,
      :created_by_document_id,
      :is_small_business,
      :short_code
    ])
    |> validate_required([
      :company_name,
      :display_name,
      :given_name,
      :family_name,
      :tax_id,
      :entity_type
    ])
    |> validate_at_least_one_created_by_identifier()
    |> unique_constraint(:created_by_document_id)
    |> unique_constraint(:created_by, name: :unique_promoter_created_by)
    |> unique_constraint(:short_code)
  end

  def create_changeset(promoter, %{"entity_type" => "organization"} = attrs) do
    promoter
    |> cast(attrs, [
      :display_name,
      :given_name,
      :family_name,
      :company_name,
      :tax_id,
      :vat_id,
      :entity_type,
      :created_by,
      :created_by_document_id,
      :is_small_business,
      :short_code
    ])
    |> validate_required([
      :company_name,
      :display_name,
      :tax_id,
      :entity_type
    ])
    |> validate_at_least_one_created_by_identifier()
    |> unique_constraint(:created_by_document_id)
    |> unique_constraint(:created_by, name: :unique_promoter_created_by)
    |> unique_constraint(:short_code)
  end

  def get_by(attrs) do
    Repo.get_by(Promoter, attrs)
  end

  defp validate_at_least_one_created_by_identifier(changeset) do
    created_by = get_field(changeset, :created_by)
    created_by_document_id = get_field(changeset, :created_by_document_id)

    if is_nil(created_by) && is_nil(created_by_document_id) do
      add_error(changeset, :created_by_identifiers, "either created_by or created_by_document_id must be present")
    else
      changeset
    end
  end
end
