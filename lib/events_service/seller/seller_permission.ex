defmodule EventsService.Seller.SellerPermission do
  @moduledoc """
  Schema for Seller Permissions. This defines what a user can do on a seller account level.
  """
  use Ecto.Schema

  import Ecto.Changeset

  alias EventsService.Seller.Seller

  @typedoc """
  SellerPermission schema. Contains information about a user's permissions on a seller account.
  """
  # styler:sort
  @type t :: %__MODULE__{
          deleted_at: DateTime.t() | nil,
          id: Ecto.UUID.t(),
          inserted_at: DateTime.t(),
          roles: [atom()],
          seller_id: Ecto.UUID.t(),
          updated_at: DateTime.t(),
          user_document_id: String.t(),
          user_id: Ecto.UUID.t()
        }

  @roles [:ADMIN, :OWNER]

  @schema_prefix :events
  @primary_key {:id, :binary_id, autogenerate: true}
  @foreign_key_type :binary_id
  # styler:sort
  schema "seller_permissions" do
    belongs_to :seller, Seller

    field :deleted_at, :utc_datetime
    field :roles, {:array, Ecto.Enum}, values: @roles
    field :user_document_id, :string
    field :user_id, Ecto.UUID

    timestamps(type: :utc_datetime)
  end

  @doc false
  def changeset(seller_permission, attrs) do
    seller_permission
    |> cast(attrs, [:seller_id, :user_id, :user_document_id, :roles, :deleted_at])
    |> validate_required([:seller_id, :roles])
    |> validate_at_least_one_user_identifier()
    |> validate_roles_inclusion(:roles)
    |> foreign_key_constraint(:seller_id)
  end

  defp validate_at_least_one_user_identifier(changeset) do
    user_id = get_field(changeset, :user_id)
    user_document_id = get_field(changeset, :user_document_id)

    if is_nil(user_id) && is_nil(user_document_id) do
      add_error(changeset, :user_identifiers, "either user_id or user_document_id must be present")
    else
      changeset
    end
  end

  defp validate_roles_inclusion(changeset, field) do
    validate_change(changeset, field, fn _, roles ->
      if Enum.all?(roles, &Enum.member?(@roles, &1)) do
        []
      else
        [{field, "Invalid role"}]
      end
    end)
  end
end
