defmodule Adyen.Clients.BaseClient do
  @moduledoc false
  @spec __using__(any) ::
          {:__block__, [],
           [{:@, [...], [...]} | {:def, [...], [...]} | {:defp, [...], [...]}, ...]}
  defmacro __using__(_) do
    quote do
      @doc """
      Build a dynamic client from a given config.
      """
      @spec client(list()) :: Tesla.Client.t()
      def client(opts \\ []) do
        middleware = [
          Tesla.Middleware.JSON,
          {Tesla.Middleware.Logger, debug: true},
          {Tesla.Middleware.Headers, ["x-api-key": get_api_key(opts)]}
        ]

        environment = Keyword.get(config(), :environment, :test)

        middleware =
          case environment do
            :test -> [{Tesla.Middleware.BaseUrl, base_url(:test, opts)} | middleware]
            :live -> [{Tesla.Middleware.BaseUrl, base_url(:live, opts)} | middleware]
            _ -> middleware
          end

        Tesla.client(middleware)
      end

      defp get_api_key(opts) do
        api_key_type = Keyword.get(opts, :api_key, :checkout_api_key)

        config()
        |> Keyword.get(:api_key)
        |> Keyword.get(api_key_type)
      end

      def config() do
        Application.get_all_env(:adyen)
      end

      def parse_response(response) do
        case response do
          {:ok, %Tesla.Env{status: status, body: body}} when status in [200, 201, 202] ->
            {:ok, body}

          {:ok, %Tesla.Env{status: 400, body: body}} ->
            {:error, %{message: body, status: :"Bad Request"}}

          {:ok, %Tesla.Env{status: 401, body: body}} ->
            {:error, %{message: body, status: :Unauthorized}}

          {:ok, %Tesla.Env{status: 403, body: body}} ->
            {:error, %{message: body, status: :Forbidden}}

          {:ok, %Tesla.Env{status: 422, body: body}} ->
            {:error, %{message: body, status: :"Unprocessable Entity"}}

          {:ok, %Tesla.Env{status: 500, body: body}} ->
            {:error, %{message: body, status: :"Internal Server Error"}}

          {:error, error} ->
            {:error, error}
        end
      end

      defp base_url(:test, opts) do
        endpoint = Keyword.get(opts, :endpoint, nil)

        case endpoint do
          :payout -> "https://pal-test.adyen.com/pal/servlet/Payout/v68"
          :balance -> "https://balanceplatform-api-test.adyen.com/btl/v3"
          :balance_platform -> "https://balanceplatform-api-test.adyen.com/bcl/v2"
          :lem -> "https://kyc-test.adyen.com/lem/v3"
          :checkout -> "https://checkout-test.adyen.com/v70"
        end
      end

      defp base_url(:live, opts) do
        endpoint = Keyword.get(opts, :endpoint, nil)
        live_prefix = Application.get_env(:adyen, :live_prefix)

        case endpoint do
          :payout ->
            "https://#{live_prefix}-pal-live.adyenpayments.com/pal/servlet/Payout/v68"

          :balance ->
            "https://balanceplatform-api-live.adyen.com/btl/v3"

          :balance_platform ->
            "https://balanceplatform-api-live.adyen.com/bcl/v2"

          :lem ->
            "https://kyc-live.adyen.com/lem/v3"

          :checkout ->
            "https://#{live_prefix}-checkout-live.adyenpayments.com/checkout/v70"
        end
      end
    end
  end
end
