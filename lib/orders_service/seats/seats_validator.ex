defmodule OrdersService.Seats.SeatsValidator do
  @moduledoc false

  alias ExServiceClient.Services.EventsService.ChannelConfig
  alias ExServiceClient.Services.EventsService.Event
  alias OrdersService.Orders.OrderItems.InitialOrderItem

  @spec validate([InitialOrderItem.t()]) :: :ok | {:error, any}
  def validate(order_items) do
    with :ok <-
           validate_multiple(&validate_seat_config_for_order_item/1, order_items),
         :ok <-
           validate_multiple(&validate_seat_category_for_order_item/1, order_items),
         :ok <-
           validate_multiple(&validate_channel_key_for_order_item/1, order_items) do
      validate_multiple(&validate_seats_for_order_item/1, order_items)
    end
  end

  defp validate_seats_for_order_item(%{seat_configs: seat_configs}) do
    validate_multiple(&validate_seat_config/1, seat_configs)
  end

  defp validate_seat_config(%{type: :SEAT}), do: :ok
  defp validate_seat_config(%{type: :GENERAL_ADMISSION}), do: :ok

  defp validate_seat_config(%{type: :TABLE}), do: :ok

  defp validate_seat_config(%{type: :BOOTH}), do: {:error, :booth_not_supported}
  defp validate_seat_config(_seat_config), do: {:error, :invalid_seat_type}

  defp validate_seat_config_for_order_item(%InitialOrderItem{event: %Event{chartKey: chart_key}} = order_item)
       when not is_nil(chart_key) do
    %InitialOrderItem{
      seat_configs: seat_configs,
      amount: amount,
      variant: %{ticketCategory: ticket_category}
    } = order_item

    seat_configs_with_label = Enum.filter(seat_configs || [], &(&1.label != nil))

    with {:external_provider, %{externalProvider: "seatsio"}} <-
           {:external_provider, ticket_category},
         {:amount, true} <-
           {:amount, Enum.count(seat_configs) == amount},
         {:with_seats, true} <-
           {:with_seats, Enum.count(seat_configs_with_label) == amount} do
      :ok
    else
      {:external_provider, _} -> :ok
      {:amount, _} -> {:error, :amount_does_not_match_seat_configs}
      {:with_seats, _} -> {:error, :label_missing_for_seat_config}
    end
  end

  defp validate_seat_config_for_order_item(_order_item), do: :ok

  defp validate_seat_category_for_order_item(%InitialOrderItem{variant: variant, seat_configs: seat_configs}) do
    with {:category_name, %{ticketCategory: %{name: category_name}}}
         when not is_nil(category_name) <-
           {:category_name, variant},
         {:matching_category, true} <-
           {:matching_category, Enum.all?(seat_configs, &(&1.category_label == category_name))} do
      :ok
    else
      {:category_name, _} -> {:error, :category_name_missing}
      {:matching_category, _} -> {:error, :category_mismatch}
    end
  end

  defp validate_channel_key_for_order_item(%{channel_config: nil, seat_configs: seat_configs}) do
    if Enum.any?(seat_configs, &(&1.channel != nil)),
      do: {:error, :channel_config_missing},
      else: :ok
  end

  defp validate_channel_key_for_order_item(%{channel_config: %ChannelConfig{channelKey: nil}}),
    do: {:error, :channel_key_missing}

  defp validate_channel_key_for_order_item(%{
         channel_config: %ChannelConfig{channelKey: _channel_key},
         seat_configs: []
       }),
       do: {:error, :seat_configs_missing_for_channel_key}

  defp validate_channel_key_for_order_item(%{
         channel_config: %ChannelConfig{channelKey: channel_key},
         seat_configs: seat_configs
       }) do
    if Enum.all?(seat_configs, &(&1.channel == channel_key)),
      do: :ok,
      else: {:error, :channel_mismatch}
  end

  defp validate_multiple(func, items) do
    items
    |> Enum.reduce([], fn item, errors ->
      case func.(item) do
        :ok ->
          errors

        {:error, error} ->
          [error | errors]
      end
    end)
    |> case do
      [] -> :ok
      errors -> {:error, errors}
    end
  end
end
